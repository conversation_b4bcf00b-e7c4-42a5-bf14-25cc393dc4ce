<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="isShowExtra"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="@dimen/category_width" />

        <TextView
            android:id="@+id/txt_promotion"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="27dp"
            android:paddingEnd="3dp"
            android:paddingTop="9dp"
            android:paddingBottom="9dp"
            android:text="@string/promotion"
            android:textColor="@color/text_grey_light"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@color/background_selected"
            tools:textColor="@color/text_orange" />

        <ImageView
            android:id="@+id/img_promo"
            android:layout_width="13dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_promotion"
            app:layout_constraintBottom_toBottomOf="@id/txt_promotion"
            app:srcCompat="@drawable/ic_local_offer_grey_24dp"
            tools:tint="@color/text_orange" />

        <TextView
            android:id="@+id/textView10"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="MAIN CATEGORY"
            android:textColor="@color/text_orange"
            android:visibility="@{isShowExtra ? View.VISIBLE : View.GONE}"
            app:fontFamily="@font/poppins_semibold"
            app:layout_constraintBottom_toTopOf="@+id/recViewCategoryMain"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_promotion"
            app:layout_constraintVertical_chainStyle="spread_inside" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recViewCategoryMain"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/textView11"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView10"
            app:layout_constraintVertical_weight="10"
            tools:listitem="@layout/list_item_category" />

        <TextView
            android:id="@+id/textView11"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:text="EXTRA CATEGORY"
            android:textColor="@color/text_orange"
            android:visibility="@{isShowExtra ? View.VISIBLE : View.GONE}"
            app:fontFamily="@font/poppins_semibold"
            app:layout_constraintBottom_toTopOf="@+id/recViewCategoryExtra"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/recViewCategoryMain" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recViewCategoryExtra"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="8dp"
            android:visibility="@{isShowExtra ? View.VISIBLE : View.GONE}"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView11"
            app:layout_constraintVertical_weight="6"
            tools:listitem="@layout/list_item_category" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_end="@dimen/bill_width" />

        <TextView
            android:id="@+id/layout_member"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="0.5dp"
            android:background="@color/colorPrimary"
            android:drawableTop="@{@drawable/ic_account_circle_grey_500_24dp}"
            android:gravity="center"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            android:text="Member"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintEnd_toStartOf="@+id/layoutNoteList"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/guideline2"
            app:layout_constraintStart_toStartOf="@+id/guideline2"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutNoteList"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="0.5dp"
            android:background="@color/colorPrimary"
            android:gravity="center"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            app:layout_constraintEnd_toStartOf="@+id/layoutTableList"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/layout_member"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_shopping_cart_white_24dp"
                tools:visibility="gone"
                app:tint="#9E9E9E" />

            <TextView
                android:id="@+id/txt_badge"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_marginLeft="17dp"
                android:background="@drawable/badge_round"
                android:gravity="center"
                android:text="0"
                android:textColor="#ffff"
                android:textSize="9sp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@+id/imageView"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/txt_note_list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/pending_bill"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imageView" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/layoutTableList"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="0.5dp"
            android:background="@color/colorPrimary"
            android:drawableTop="@{@drawable/ic_event_seat_grey_500_24dp}"
            android:gravity="center"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            android:text="@string/table_list"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/layoutNoteList"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="layoutTableList,layoutNoteList,layout_member" />

        <TextView
            android:id="@+id/textView15"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="0.5dp"
            android:background="@color/colorPrimary"
            android:gravity="center_horizontal"
            android:padding="3dp"
            android:text="ORDER #1"
            android:textColor="@color/text_grey_light"
            android:textSize="17sp"
            android:visibility="gone"
            app:fontFamily="@font/poppins"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline2"
            app:layout_constraintTop_toBottomOf="@+id/layout_member" />

        <View
            android:id="@+id/layoutPayment"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/greeen_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline2"
            app:layout_constraintTop_toTopOf="@+id/txt_finish" />

        <TextView
            android:id="@+id/txt_finish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginLeft="32dp"
            android:paddingTop="11dp"
            android:paddingBottom="11dp"
            android:text="@string/payment"
            android:textColor="#ffff"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/layoutPayment" />

        <TextView
            android:id="@+id/txtGrandTotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:text="0"
            android:textColor="#ffff"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintBaseline_toBaselineOf="@+id/txt_finish"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="125,000" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recviewTax"
            android:layout_width="0dp"
            android:layout_height="75dp"
            android:layout_marginBottom="3dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/layout_tax_detail"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline2"
            tools:listitem="@layout/list_item_tax" />

        <include
            android:id="@+id/layout_tax_disc"
            layout="@layout/layout_tax"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="3dp"
            app:layout_constraintBottom_toTopOf="@+id/layout_tax_detail"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline2" />

        <androidx.cardview.widget.CardView
            android:id="@+id/layout_tax_detail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardUseCompatPadding="true"
            app:layout_constraintBottom_toTopOf="@+id/layoutPayment"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline2">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:padding="5dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Tax and Gratuity Detail"
                    android:textColor="@color/grey_light" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
                    app:tint="@color/grey_light" />
            </RelativeLayout>
        </androidx.cardview.widget.CardView>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recViewBill"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="1dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/layout_tax_disc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/guideline2"
            app:layout_constraintTop_toBottomOf="@+id/barrier1"
            tools:listitem="@layout/list_item_bill" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recViewMenuExtra"
            android:layout_width="@dimen/menu_widht"
            android:layout_height="0dp"
            android:visibility="@{isShowExtra ? View.VISIBLE : View.GONE}"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/txt_check_order"
            tools:listitem="@layout/list_item_menu" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recViewMenuMain"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/recViewMenuExtra"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@id/txt_check_order"
            app:spanCount="@integer/menu_span_count"
            tools:listitem="@layout/list_item_menu" />

        <FrameLayout
            android:id="@+id/container_main"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline2"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@id/txt_check_order" />

        <TextView
            android:id="@+id/layout_warning"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/text_orange"
            android:padding="5dp"
            tools:text="5 transaksi belum tersingkronkan"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/recViewMenuExtra"
            app:layout_constraintStart_toStartOf="@+id/guideline"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/layout_error"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#E61C1C1C"
            android:padding="5dp"
            tools:text="2 item masuk pending print!"
            android:textColor="@color/red_background"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/recViewMenuExtra"
            app:layout_constraintStart_toStartOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@id/layout_warning"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/txt_pending_order"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/red_background"
            android:fontFamily="@font/poppins"
            android:paddingStart="3dp"
            android:paddingTop="3dp"
            android:text="@string/pending_order_count"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/recViewMenuExtra"
            app:layout_constraintStart_toStartOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@id/layout_error"
            tools:text="3 new pending orders"
            tools:visibility="visible" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/txt_pending_order"
            app:layout_constraintEnd_toEndOf="@id/txt_pending_order"
            app:layout_constraintStart_toStartOf="@id/txt_pending_order"
            app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp" />

        <TextView
            android:id="@+id/txt_check_order"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/red_background"
            android:paddingLeft="3dp"
            android:paddingBottom="3dp"
            android:text="CHECK"
            android:textColor="@color/white"
            android:textSize="13sp"
            android:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toEndOf="@+id/recViewMenuExtra"
            app:layout_constraintStart_toStartOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@id/txt_pending_order" />

        <include
            android:id="@+id/layout_tutorial_product"
            layout="@layout/layout_tutorial_add_product"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/recViewMenuExtra"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintTop_toBottomOf="@id/txt_check_order" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
