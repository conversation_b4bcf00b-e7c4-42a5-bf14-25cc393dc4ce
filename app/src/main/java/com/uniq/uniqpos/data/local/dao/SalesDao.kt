package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.model.Discount
import com.uniq.uniqpos.model.DiscountInfo
import com.uniq.uniqpos.model.Order
import retrofit2.http.GET

/**
 * Created by ANNASBlackHat on 22/10/2017.
 */

@Dao
interface SalesDao {

    @Query("SELECT * FROM sales WHERE timeCreated >= :minTimeMillis AND timeCreated <= :maxTimeMillis ORDER BY timeCreated DESC")
    fun salesHistoryToday(minTimeMillis: Long, maxTimeMillis: Long): LiveData<List<SalesEntity>>

    @Query("SELECT * FROM sales WHERE openShiftId = :openShiftFkid ORDER BY timeCreated DESC")
    fun salesHistoryByOpenShift(openShiftFkid: Long): LiveData<List<SalesEntity>>

    @Query("SELECT * FROM sales WHERE noNota in (:ids)")
    suspend fun salesHistoryByIds(ids: List<String>): List<SalesEntity>

    @Query("SELECT * FROM sales WHERE synced = 0")
    fun getUnsyncedSales(): List<SalesEntity>

    @Query("SELECT COUNT() FROM sales WHERE synced = 0")
    fun countUnsyncedSales(): Int

    @Query("SELECT sum(grandTotal) FROM sales WHERE timeCreated >= :minTimeMillis AND timeCreated <= :maxTimeMillis")
    fun getTotalSalesToday(minTimeMillis: Long, maxTimeMillis: Long): LiveData<Int>

    @Query("SELECT sum(grandTotal) FROM sales WHERE openShiftId = :openShiftId AND payment NOT LIKE '%COMPLIMENT%' AND payment NOT LIKE '%DUTY MEALS%' AND status='Success'")
    fun getTotalSalesByShift(openShiftId: Long): LiveData<Int>

    @Query("SELECT sum(grandTotal) FROM sales WHERE (timeCreated >= :minTimeMillis AND timeCreated <= :maxTimeMillis) AND timeCreated > :lastRecap AND status='Success' AND (payment = 'CASH' OR payment='CARD' OR payment='PIUTANG')")
    fun getTotalSalesToday(minTimeMillis: Long, maxTimeMillis: Long, lastRecap: Long): LiveData<Int>

    @Query("SELECT * FROM sales WHERE openShiftId IN(:openShiftId)  ORDER BY timeCreated")
    fun getSalesRecap(vararg openShiftId: Long): List<SalesEntity>

    @Query("SELECT * FROM sales WHERE noNota = :id LIMIT 1")
    fun getSalesById(id: String): SalesEntity?

    @Query("SELECT * FROM sales WHERE noNota = :id LIMIT 1")
    fun getSalesByIdLive(id: String): LiveData<SalesEntity?>

    @Query("SELECT COUNT(*) FROM sales WHERE timeCreated >= :startDate AND timeCreated <= :endDate")
    fun countSalesToday(startDate: Long, endDate: Long): Int

    @Query("SELECT COUNT(*) FROM sales WHERE noNota = :noNota")
    fun countSalesById(noNota: String): Int

    @Query("SELECT noNota FROM sales where openShiftId = :openShiftId")
    fun getSalesIdsByShift(openShiftId: Long): List<String>

    //only call for after payment, because any conflict will be ignored
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun saveSaleAfterPayment(vararg salesEntity: SalesEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveSales(salesList: List<SalesEntity>)

    @Query("select max(openShiftId) from sales")
    fun getLastOpenShiftId(): Long?

    @Query("select count(*) from sales where openShiftId = :id")
    fun getSalesCountByOpenShiftId(id: Long): Int

    @Query("SELECT * FROM tmp_sales WHERE status = 'pending' AND outletId = :outletid")
    fun getTmpSales(outletid: Int): LiveData<List<TmpSalesEntity>>

    @Query("SELECT * FROM tmp_sales WHERE status = 'pending' AND outletId = :outletid")
    fun getTmpSalesLocal(outletid: Int?): List<TmpSalesEntity>

    @Query("SELECT * FROM tmp_sales WHERE status = 'pending' AND outletId = :outletid")
    fun getTransactionCart(outletid: Int?): List<TmpSalesEntity>

    @Query("SELECT * FROM tmp_sales WHERE status = 'pending' AND outletId = :outletid ORDER BY timeModified DESC")
    fun getTransactionCartLive(outletid: Int?): LiveData<List<TmpSalesEntity>>

    @Query("SELECT COUNT(*) FROM tmp_sales WHERE status = 'pending'")
    fun getTransactionCartCount(): LiveData<Int>

    @Query("SELECT * FROM tmp_sales WHERE synced = 0")
    fun getUnsyncedTmpSales(): List<TmpSalesEntity>

    @Query("SELECT COUNT(*) FROM tmp_sales WHERE synced = 0")
    fun countUnsyncedTmpSales(): Int

    @Query("SELECT * FROM tmp_sales WHERE synced = 1 AND status = 'paid'")
    fun getSyncedAndPaidTmpSales(): List<TmpSalesEntity>

    @Query("DELETE FROM tmp_sales WHERE synced = 1 AND status = 'paid'")
    fun deleteSyncedAndPaidTmpSales()

    @Query("DELETE FROM tmp_sales WHERE synced = 1 AND status = 'paid' and timeModified > :timeMillis ")
    fun deleteSyncedAndPaidTmpSalesByTime(timeMillis: Long)

    @Query("SELECT * FROM tmp_sales WHERE noNota = :nota")
    fun getTmpSalesById(nota: String): TmpSalesEntity?

    @Query("SELECT * FROM tmp_sales WHERE noNota = :id LIMIT 1")
    suspend fun getSalesCartById(id: String): TmpSalesEntity

    @Update
    fun updateTmpSale(tmpSalesEntity: TmpSalesEntity)

    @Delete
    fun deleteTmpSale(tmpSalesEntity: TmpSalesEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveTmpSales(tmpSalesEntity: TmpSalesEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveTmpSalesList(tmpSalesEntity: List<TmpSalesEntity>)

    @Update
    fun updateSales(salesEntity: SalesEntity)

    @Query("select DISTINCT receiptReceiver from sales where length(receiptReceiver) >= 10 limit 100")
    suspend fun getReceiptReceivers(): List<String>

    @Query("select DISTINCT customer from sales where length(customer) > 2 limit 50 ")
    suspend fun fetchCustomerNames(): List<String>

    @Query("select discountInfo, voucherInfo from sales where discount > 0 or voucher > 0 order by timeCreated desc limit 50")
    suspend fun fetchDiscountAndVoucherInfo(): List<DiscountInfo>

    @Query("delete from sales where timeCreated < :maxDate")
    fun clearOldSales(maxDate: Long)

    //  --------  CASH RECAP --------------------

    @Query("SELECT * FROM cash_recap ORDER BY timeCreated DESC")
    fun getCashRecap(): LiveData<List<CashRecapEntity>>

    @Query("SELECT * FROM cash_recap WHERE synced=0")
    fun getUnsyncedCashRecap(): List<CashRecapEntity>

    @Query("SELECT MAX(timeCreated) FROM cash_recap")
    fun getLastCashRecap(): Long

    @Query("SELECT COUNT(*) FROM cash_recap WHERE timeCreated >= :timeStart")
    fun countCashRecapByTime(timeStart: Long): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveCashRecap(cashRecapEntity: CashRecapEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveCashRecaps(cashRecaps: List<CashRecapEntity>)

    @Query("DELETE FROM cash_recap where id = :id")
    fun deleteCashRecapById(id: Long)

    //  --------  REFUND --------------------

    @Query("SELECT * FROM refund WHERE synced = 0")
    fun getUnsyncedRefund(): List<RefundEntity>

    @Insert
    fun saveRefund(refundEntity: RefundEntity)

    @Update
    fun updateRefund(refundEntity: RefundEntity)

    @Query("SELECT * FROM pending_print")
    suspend fun getPendingPrint(): List<PendingPrintEntity>

    @Query("SELECT COUNT(*) FROM pending_print")
    fun countPendingPrint(): LiveData<Int>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun savePendingPrint(pendingPrintEntity: List<PendingPrintEntity>)

    @Delete
    fun deletePendingPrint(pendingPrintEntity: PendingPrintEntity)

    @Delete
    fun deletePendingPrint(list: List<PendingPrintEntity>)

    @Query("SELECT * FROM reservation")
    fun getReservationsLive(): LiveData<List<ReservationEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveReservation(reservations: ReservationEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveReservations(reservations: List<ReservationEntity>)

    @Update
    fun updateReservation(reservationEntity: ReservationEntity)

    //PIUTANG -------------------------

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun savePiutang(piutangEntity: PiutangEntity): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun savePiutangList(piutangEntity: List<PiutangEntity>)

    @Query("UPDATE piutang SET piutangId = :newId WHERE piutangId = :oldId")
    fun updatePiutangId(oldId: Long, newId: Long)

    @Update
    fun updatePiutang(piutangEntity: PiutangEntity)

    @Query("SELECT * FROM piutang")
    fun getPiutangLive(): LiveData<List<PiutangEntity>>

    @Query("SELECT * FROM piutang where salesFkid = :salesId LIMIT 1")
    fun getPiutangBySalesId(salesId: String): PiutangEntity?

    @Query("SELECT * FROM piutang where salesFkid = :salesId LIMIT 1")
    fun getPiutangBySalesIdLive(salesId: String): LiveData<PiutangEntity?>

    @Query("SELECT * FROM piutang where salesFkid in (:salesIds) LIMIT 1")
    fun getPiutangBySalesIds(salesIds: List<String>): List<PiutangEntity>

    @Delete
    fun deletePiutang(piutangEntity: PiutangEntity)

    @Query("UPDATE piutang SET unpaid=unpaid- :pay WHERE piutangId = :id")
    fun updateUnpaidPiutang(pay: Int, id: Long)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun savePiutangHistory(piutangHistoryEntity: PiutangHistoryEntity): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun savePiutangHistoryList(piutangHistoryEntity: List<PiutangHistoryEntity>)

    @Query("UPDATE piutang_history SET piutangHistoryId = :newId WHERE piutangHistoryId = :oldId")
    fun updatePiutangHistoryId(newId: Long, oldId: Long)

    @Update
    fun updatePiutangHistory(piutangHistoryEntity: PiutangHistoryEntity)

    @Query("update piutang_history set piutangFkid = :newId where piutangFkid = :oldId")
    fun updatePiutangIdOnHistory(oldId: Long, newId: Long)

    @Query("SELECT * FROM piutang_history WHERE piutangFkid = :id")
    fun getPiutangHistoryListById(id: Long): LiveData<List<PiutangHistoryEntity>>

    @Query("SELECT * FROM piutang_history WHERE synced = 0")
    fun getUnsyncedPiutangHistory(): List<PiutangHistoryEntity>

    @Query("SELECT sum(total) from piutang_history where dataCreated > :startDate AND dataCreated < :endDate")
    fun getTotalPiutangPayment(startDate: Long, endDate: Long): Int

    @Query("SELECT * FROM piutang_history WHERE dataCreated > :startDate AND dataCreated < :endDate")
    fun getPiutangHistory(startDate: Long, endDate: Long): List<PiutangHistoryEntity>

    @Query("SELECT * FROM shift")
    fun getAvailableShift(): List<ShiftEntity>

    //ORDER SALES ------------
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveOrderSales(orders: List<OrderSalesEntity>)

    @Update
    fun updateOrderSales(order: OrderSalesEntity)

    @Query("SELECT * FROM order_sales WHERE status in ('pending', 'accept', 'payment_verified', 'ready') ORDER BY timeOrder DESC") // WHERE status in ('pending', 'payment_verified', 'ready')
    fun getOrderSales(): LiveData<List<OrderSalesEntity>>

    @Query("SELECT * FROM order_sales WHERE synced = 0")
    fun getUnsyncOrderSales(): List<OrderSalesEntity>

    @Query("select count(*) from order_sales where status='pending'")
    fun countPendingOrder(): LiveData<Int>

    @Query("select * from order_sales where orderSalesId = :id")
    fun getOrderSalesById(id: String): OrderSalesEntity?


    // ----------- OPENS SHIFT ---------------
    @Query("SELECT * FROM open_shift ORDER BY timeOpen DESC LIMIT 10")
    suspend fun getOpenShift(): List<OpenShiftEntity>

    @Query("SELECT * FROM open_shift WHERE openShiftId = :id")
    fun getOpenShiftById(id: Long): OpenShiftEntity?

    @Query("delete from open_shift where timeOpen < :maxDate")
    fun clearOldOpenShift(maxDate: Long)

    @Query("select max(timeCreated) from sales")
    fun maxSales(): Long

    @Query("select min(timeCreated) from sales")
    fun oldestSales(): Long

    @Query("UPDATE open_shift SET timeClose = :timeClose where openShiftId = :id")
    fun updateOpenShift(id: Long, timeClose: Long)

    // -------- Sales Tag --------

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addSalesTag(salesTags: List<SalesTagEntity>)

    @Query("select * from sales_tag where dataStatus='on'")
    fun getSalesTagLive(): LiveData<List<SalesTagEntity>>
}