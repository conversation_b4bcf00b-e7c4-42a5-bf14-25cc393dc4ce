package com.uniq.uniqpos.view.transaction.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.safe
import java.text.NumberFormat
import java.util.*

/**
 * Reusable composable for displaying order information
 * Used by both NewOrderDialog and PaidOrderDialog
 */
@Composable
fun OrderDisplayContent(
    salesEntity: SalesEntity,
    products: List<ProductEntity>,
    headerContent: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Header content (customizable by caller)
        item {
            headerContent()
        }

        item { Spacer(modifier = Modifier.height(8.dp)) }

        // Customer Information
        item {
            Text(
                text = salesEntity.customer.safe(),
                fontSize = 22.sp,
                fontWeight = FontWeight.Bold
            )
        }

        // Receipt Receiver (if available)
        if (!salesEntity.receiptReceiver.isNullOrBlank()) {
            item {
                Text(
                    text = salesEntity.receiptReceiver.safe(),
                    fontSize = 16.sp,
                    color = Color.Gray
                )
            }
        }

        item { Spacer(modifier = Modifier.height(8.dp)) }

        // Order Metadata (Order ID and Table)
        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Order #${salesEntity.displayNota}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
                if (salesEntity.table.isNotBlank()) {
                    Text(
                        text = "Table: ${salesEntity.table}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal
                    )
                }
            }
        }

        item { Spacer(modifier = Modifier.height(8.dp)) }
        item { HorizontalDivider() }
        item { Spacer(modifier = Modifier.height(8.dp)) }

        // Items Header
        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "QTY",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.width(40.dp)
                )
                Text(
                    text = "ITEM",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    text = "SUBTOTAL",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.End
                )
            }
        }

        item { Spacer(modifier = Modifier.height(8.dp)) }

        // Items List
        items(salesEntity.orderList ?: emptyList()) { item ->
            OrderItemRow(item = item, products = products)
        }

        item { Spacer(modifier = Modifier.height(8.dp)) }
        item { HorizontalDivider() }
        item { Spacer(modifier = Modifier.height(8.dp)) }

        // Total Section
        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Total",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = formatCurrency(salesEntity.grandTotal.toDouble()),
                    fontSize = 22.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun OrderItemRow(item: Order, products: List<ProductEntity>) {
    // Find product details from the products list
    val product = products.find { it.productDetailId == item.product?.productDetailId }
    val productName = product?.name ?: item.product?.name ?: "Unknown Product"

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Top
    ) {
        // Quantity
        Text(
            text = item.qty.toString(),
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.width(40.dp)
        )

        // Item Details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = productName,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            // Show modifiers/extras if any
            if (item.extra.isNotEmpty()) {
                val modifiers = item.extra.mapNotNull { extra ->
                    val extraProduct = products.find { it.productDetailId == extra.product?.productDetailId }
                    extraProduct?.name ?: extra.product?.name
                }
                if (modifiers.isNotEmpty()) {
                    Text(
                        text = "+ ${modifiers.joinToString(", ")}",
                        fontSize = 12.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                    )
                }
            }

            // Show note if any
            if (!item.note.isNullOrBlank()) {
                Text(
                    text = "Note: ${item.note}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                )
            }
        }

        // Subtotal
        Text(
            text = formatCurrency(item.subTotal.toDouble()),
            fontSize = 14.sp,
            textAlign = TextAlign.End
        )
    }
}

private fun formatCurrency(amount: Double): String {
    val formatter = NumberFormat.getCurrencyInstance(Locale("id", "ID"))
    return formatter.format(amount)
}
