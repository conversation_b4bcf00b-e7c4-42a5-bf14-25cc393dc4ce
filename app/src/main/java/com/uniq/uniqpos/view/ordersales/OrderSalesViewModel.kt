package com.uniq.uniqpos.view.ordersales

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import com.uniq.uniqpos.util.receipt.NotaManager
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by annasblack<PERSON> on 2019-07-25
 */
class OrderSalesViewModel @Inject constructor(
    private val salesRepository: SalesRepository,
    private var settingRepository: SettingRepository,
    private var productRepository: ProductRepository,
    private val sharedPref: SharedPref,
) : BaseViewModel() {

//    private val viewModelJob = Job()
//    private val uiScope = CoroutineScope(Dispatchers.Main + viewModelJob)

    val dialogTaskCommand = SingleLiveEvent<String>()
    val printTask = SingleLiveEvent<List<PendingPrintEntity>>()

    val taskNavigateOrderSalesDetail = SingleLiveEvent<OrderSalesEntity>()
    val taskNavigateSalesDetail = SingleLiveEvent<SalesEntity>()

    val orderSalesList = ArrayList<OrderSalesEntity>()
    val productList = ArrayList<ProductEntity>()
    val gratuityList = ArrayList<GratuityEntity>()
    private val tmpSalesConverted = HashMap<String, SalesEntity>()

    var currentSalesEntityPaid = mutableStateOf<SalesEntity?>(null)

    fun getOrderSalesLive(outletId: Int) = salesRepository.getOrderSalesLive(outletId)

    val outlet: Outlet? by lazy { sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) }
    val employee: Employee? by lazy { sharedPref.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java) }

    fun updateOrderSalesById(
        orderId: String,
        status: String,
        info: String?,
        outlet: Outlet,
        employee: Employee,
        shift: ShiftOpen
    ) {
        orderSalesList.firstOrNull { it.orderSalesId == orderId }?.let { order ->
            when (status) {
                "accept" -> order.timeAcceptReject = System.currentTimeMillis()
                "ready" -> order.timeReady = System.currentTimeMillis()
                "taken" -> order.timeTaken = System.currentTimeMillis()
                "reject" -> {
                    order.timeAcceptReject = System.currentTimeMillis()
                    order.rejectReason = info
                }
            }
            order.synced = false
            order.status = status

            var isError = false
            if (!isError) {
                Timber.i("update this : ${Gson().toJson(order.copy(items = ""))}")
//                uiScope.launch { salesRepository.updateOrderSales(order) }
//                viewModelScope.launch { salesRepository.updateOrderSales(order) }
                viewModelScope.launch { salesRepository.updateOrderSalesStatus(order, employee.employeeId, info) }
            }
        }
    }

    fun setData(items: List<OrderSalesEntity>) {
        orderSalesList.clear()
        Timber.i("total order sales: ${items.size}")
        items.firstOrNull()?.let { item ->
            Timber.i("first item: ${Gson().toJson(item)}")
        }
        Timber.i("order sales count: ${items.groupingBy { it.status }.eachCount()}")
        viewModelScope.launch {
            items.forEach {
                try {
                    var sales: SalesEntity
                    if (tmpSalesConverted.containsKey(it.orderSalesId)) {
                        sales = tmpSalesConverted[it.orderSalesId]!!
                    } else {
                        sales = Gson().fromJson(it.items, SalesEntity::class.java)
                        //handle if product name to set (from backend)
                        sales.orderList?.forEach { item ->
                            if (item.product?.name.safe().isEmpty()) {
                                productList.firstOrNull { it.productDetailId == item.product?.productDetailId }
                                    ?.let { product ->
                                        item.product = product
                                    }
                            }
                        }
                    }

                    //cache it
                    tmpSalesConverted[it.orderSalesId] = sales
                    val orderAdjusted = it.adjustData()
                    it.customer = orderAdjusted.customer
                    it.grandTotal = orderAdjusted.grandTotal
                    it.itemNames = orderAdjusted.itemNames
                } catch (e: Exception) {
                    it.customer = "Error"
                    Timber.i("convert error - $e")
                }
            }
        }
        orderSalesList.addAll(items)
    }

    fun savePendingPrint(pendingPrintEntity: List<PendingPrintEntity>) {
        viewModelScope.launch {
            salesRepository.savePendingPrint(pendingPrintEntity)
        }
    }

    fun syncOrderSales(outletId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            salesRepository.syncOrderSales(outletId)
        }
    }

    fun fetchSalesEntity(orderSales: OrderSalesEntity) {
        viewModelScope.launch {
            val salesList = salesRepository.getSalesByIds(listOf(orderSales.orderSalesId))
            Timber.i("fetching salesEntity '${orderSales.orderSalesId}', is in history? ${salesList.isNotEmpty()}, status ${orderSales.status}")
            if (salesList.isNotEmpty()) {
                val sales = salesList.takeIf { it.size > 1 }?.firstOrNull { it.status == "Refund" }
                    ?: salesList.first()
                sales.salesTag = null
                taskNavigateSalesDetail.postValue(sales)
            } else {
                taskNavigateOrderSalesDetail.postValue(orderSales)
            }
        }
    }

    fun loadProduct() {
        viewModelScope.launch {
            productList.clear()
            val products = awaitBackgroundList { productRepository.getProduct() }
            productList.addAll(products)
        }
    }

    fun loadGratuity(){
        viewModelScope.launch {
            gratuityList.clear()
            val gratuities = awaitBackgroundList { productRepository.getGratuityList() }
            gratuityList.addAll(gratuities)
        }
    }

    fun getUpdateStatusAvailable(currentStatus: String, orderType: String): List<String> {
        if (currentStatus == "pending") {
            return listOf("Accept", "Reject")
        }else if(currentStatus == "payment_verified"){
            return if(orderType == "pickup") listOf("Send") else listOf("Ready")
        }else if(currentStatus == "ready"){
            return if(orderType == "pickup") listOf("Delivered") else listOf("Taken")
        }
        return listOf()
    }

    fun findSales(salesId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            salesRepository.syncSalesIds(arrayListOf(salesId))
            repeat(3) { attempt ->
                val result = try {
                    salesRepository.getSalesByIds(listOf(salesId))
                } catch (e: Exception) {
                    Timber.i(e, "Error fetching Sales (attempt ${attempt + 1})")
                    null
                }

                Timber.i("sales find id $salesId attempt ${attempt + 1} : ${result?.size}")
                if (result != null && result.isNotEmpty()) {
//                    taskShowSalesPaid.postValue(result.first())
                    currentSalesEntityPaid.value = result.first()
                    return@launch // done
                }

                // if not last attempt, wait a bit before next try
                if (attempt < 2) delay(1000L)
            }

            Timber.w("findSales: no result after 3 attempts for $salesId")
            _dialogMsg.postValue(Event("ada order baru terbayarkan, pastikan koneksi internet lancar, dan silahkan coba cek Riwayat Transaksi untuk mencetak Nota!"))
        }
    }

    fun printSales(sales: SalesEntity) {
        viewModelScope.launch {
            val printers = settingRepository.getPrinterList()
            Timber.i("printer size : ${printers.size}")
            val ticketList = settingRepository.getPrinterTicketOrders()
            val printDataList = NotaManager.createNota(
                printers,
                ticketList,
                sales,
                outlet!!,
                employee!!,
            )
            printTask.postValue(printDataList)
//            printTask.postValue(Pair(printDataList, isCloseAfterPrint))
        }
    }

}