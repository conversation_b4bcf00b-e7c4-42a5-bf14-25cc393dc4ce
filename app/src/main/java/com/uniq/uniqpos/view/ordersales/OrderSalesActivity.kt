package com.uniq.uniqpos.view.ordersales

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.databinding.ActivityOrderSalesBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.printer.PrinterSocket
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.transaction.dialog.NewOrderDialog
import com.uniq.uniqpos.view.transaction.dialog.PaidOrderDialog
import com.uniq.uniqpos.view.transactionhistory.HistoryDetailActivity
import timber.log.Timber

class OrderSalesActivity : BaseActivity<OrderSalesViewModel, ActivityOrderSalesBinding>() {

    override fun getLayoutRes() = R.layout.activity_order_sales
    override fun getViewModel() = OrderSalesViewModel::class.java

    private lateinit var adapter: ViewPagerAdapter

    //private lateinit var orderSalesPending: OrderSalesListFragment
//    private lateinit var orderSalesAccept: OrderSalesListFragment
//    private lateinit var orderSalesReady: OrderSalesListFragment
    private val orderSalesPages = ArrayList<OrderSalesListFragment>()
    private lateinit var listener: OrderSalesListener
    private lateinit var broadcastReceiver: BroadcastReceiver
    private var selectedPage = 0
    private var isUseTabLayout = false

    private var showNewOrderDialog = mutableStateOf(false)
    private var currentOrderSales = mutableStateOf<OrderSalesEntity?>(null)
    private var currentSalesEntity = mutableStateOf<SalesEntity?>(null)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.elevation = 0.0f

        viewModel.loadProduct()
        viewModel.loadGratuity()
        val outletId = outlet()?.outletId ?: 0

        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.getStringExtra("sync")) {
                    "order_sales" -> viewModel.syncOrderSales(outletId)
                    "payment_receive" -> {
                        val salesId = intent.getStringExtra("id")
                        Timber.i("received payment order app, id: $salesId")
                        viewModel.findSales(salesId.safe())
                    }
                }
            }
        }
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(
                    broadcastReceiver,
                    IntentFilter(Constant.INTENT_SYNC_REQUEST),
                    RECEIVER_NOT_EXPORTED
                )
            } else {
                @Suppress("UnspecifiedRegisterReceiverFlag")
                registerReceiver(broadcastReceiver, IntentFilter(Constant.INTENT_SYNC_REQUEST))
            }
        } catch (e: Exception) {
            Timber.i("register receiver error $e")
        }

//        isUseTabLayout = resources.getBoolean(R.bool.landscape_only)
    }


    override fun initView() {
        super.initView()

        listener = object : OrderSalesListener {
            override fun getOrderSales() = viewModel.orderSalesList
            override fun onItemSelected(orderSales: OrderSalesEntity) {
                viewModel.fetchSalesEntity(orderSales)
            }

            override fun updateStatus(orderId: String, status: String, info: String?) {
                Timber.i("update to : $status")
                when(status){
                    "reject" -> {
                        showRejectReasonDialog(orderId)
                    }
                    "accept", "ready", "taken" -> {
                        <EMAIL>("ubah status order #${orderId} menjadi $status?", "KONFIRMASI", {_,_->
                            viewModel.updateOrderSalesById(
                                orderId,
                                status,
                                info,
                                outlet()!!,
                                employee()!!,
                                shiftOpen()!!
                            )
                        })
                    }
                    else -> toast("status tidak valid")
                }
            }
        }

        adapter = ViewPagerAdapter(supportFragmentManager)

        val statusFilter = intent.getStringExtra("status_filter").safe()
        val statusList = mapOf("reject" to "Reject")
        val pageFilter = HashMap<String, String>()
        if (statusFilter.isNotBlank()) {
            statusFilter.split(",").forEach { key ->
                statusList[key]?.let { v ->
                    pageFilter[key] = v
                }
            }
        }

        employee()
        //key: status (based on db), value: title displayed in app
        val pages = if (pageFilter.isEmpty()) mapOf(
            "pending" to "Pending",
            "accept" to "Accept",
            "payment_verified" to "Paid",
            "ready" to "Ready"
        ) else pageFilter
        for (page in pages) {
            val fragmentPage = OrderSalesListFragment.newInstance(page.key, listener)
            orderSalesPages.add(fragmentPage)
            adapter.addFragment(fragmentPage, page.value)
        }

        binding.viewPager.adapter = adapter
        binding.tablayout.post { binding.tablayout.setupWithViewPager(binding.viewPager) }

        binding.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {}
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                selectedPage = position
                refreshPage(position)
            }
        })

        setupNewOrderDialog()
    }

    override fun observeTask() {
        setupToastMessage(this, viewModel.toastMessage)
        setupDialogMessage(this, viewModel.dialogMsg)

        viewModel.dialogTaskCommand.observe(this) {
            showMessage(it)
        }

        viewModel.taskNavigateSalesDetail.observe(this) { sales ->
            val intent =
                Intent(this, HistoryDetailActivity::class.java)
            intent.putExtra("data", sales)
            startActivity(intent)
        }

        viewModel.taskNavigateOrderSalesDetail.observe(this) { orderSales ->
            if (orderSales.status == "pending") {
                val salesEntity = try {
                    Gson().fromJson(orderSales.items, SalesEntity::class.java)
                } catch (e: Exception) {
                    Timber.e("Error parsing OrderSalesEntity JSON: ${e.message}")
                    toast("failed parsing sales data")
                    null
                }

                currentOrderSales.value = orderSales
                currentSalesEntity.value = salesEntity
                showNewOrderDialog.value = true
            } else {
                val intent =
                    Intent(this, OrderSalesDetailActivity::class.java)
                val orderSalesJson = Gson().toJson(orderSales)
//            intent.putExtra("data", orderSales)
                intent.putExtra("data", orderSalesJson)
                Timber.i("taskNavigateOrderSalesDetail: $orderSalesJson")
                startActivity(intent)
            }
        }
    }

    override fun observeData() {
        showDialog(true) //first lets show dialog for loading....

        viewModel.getOrderSalesLive(outlet()?.outletId ?: 0)
            .observe(this) { resource ->
                Timber.d("#ordersales : $resource")
                resource?.data?.let { items ->
                    viewModel.setData(items)
                    refreshPage(selectedPage)
                }
                showDialog(false)//dismiss loading once received any data
            }

        viewModel.printTask.observe(this) {
            it?.let { printList ->
                managePrintWifi(
                    printList
                ) { isConnected, message ->
                    if (!isConnected) {
                        viewModel.savePendingPrint(printList)
                    }
                }
            }
        }
    }

    private fun refreshPage(page: Int) {
        orderSalesPages.getSafe(page)?.refreshItem()
//        when(page) {
//            0 -> orderSalesPending.refreshItem()
//            1 -> orderSalesAccept.refreshItem()
//            2 -> orderSalesReady.refreshItem()
//        }
    }

    private fun setupNewOrderDialog() {
        binding.composeOverlay.apply {
            setContent {
                if (showNewOrderDialog.value &&
                    currentOrderSales.value != null &&
                    currentSalesEntity.value != null
                ) {
                    NewOrderDialog(
                        salesEntity = currentSalesEntity.value!!,
                        products = emptyList(),
                        onAccept = {
                            listener.updateStatus(currentOrderSales.value?.orderSalesId.safe(), "accept")
                        },
                        onReject = {
                            listener.updateStatus(currentOrderSales.value?.orderSalesId.safe(), "reject")
                        },
                        onDismissRequest = {
                            showNewOrderDialog.value = false
                        },
                        onClose = {
                            showNewOrderDialog.value = false
                        }
                    )
                }else if(viewModel.currentSalesEntityPaid.value != null){
                    PaidOrderDialog(viewModel.currentSalesEntityPaid.value!!,
                        emptyList(),
                        {
                            viewModel.printSales(viewModel.currentSalesEntityPaid.value!!)
                            viewModel.currentSalesEntityPaid.value = null
                        },
                        {
                            viewModel.currentSalesEntityPaid.value = null
                        },
                        {})
                }
            }
        }

    }

    private fun showRejectReasonDialog(orderId: String) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if(result.isNotBlank()){
                viewModel.updateOrderSalesById(
                    orderId,
                    "reject",
                    result,
                    outlet()!!,
                    employee()!!,
                    shiftOpen()!!
                )
                bottomDialog.dismiss()
            }
        }
        bottomDialog.setModel(BottomDialogModel("REJECT ORDER", "SAVE", "Reject Reason", "Give a reason why this order is rejected", "Reason"))
        bottomDialog.show(supportFragmentManager, "reject-order")
    }


    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_order_sales, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_reject -> {
//                startActivity(Intent(this, OrderSalesRejectActivity::class.java))
                val intent = Intent(this, OrderSalesActivity::class.java)
                intent.putExtra("status_filter", "reject")
                startActivity(intent)
            }

            else -> finish()
        }
        return super.onOptionsItemSelected(item)
    }

}
