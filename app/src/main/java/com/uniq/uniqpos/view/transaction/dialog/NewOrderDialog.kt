package com.uniq.uniqpos.view.transaction.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity

/**
 * New Online Order Dialog using Jetpack Compose
 * Displays incoming order details with Accept/Reject actions
 */
@Composable
fun NewOrderDialog(
    salesEntity: SalesEntity,
    products: List<ProductEntity>,
    onAccept: () -> Unit,
    onReject: () -> Unit,
    onClose: () -> Unit,
    onDismissRequest: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .widthIn(min = 300.dp, max = 560.dp) // Material 3 responsive width
                .fillMaxWidth(0.9f) // Use 90% of available width, but respect min/max
                .fillMaxHeight(0.9f) // Limit dialog height to 90% of screen
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
            ) {
                // Scrollable content using the reusable OrderDisplayContent
                OrderDisplayContent(
                    salesEntity = salesEntity,
                    products = products,
                    headerContent = {
                        // Header with bell icon and title
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "🔔",
                                fontSize = 20.sp,
                                modifier = Modifier.padding(end = 8.dp)
                            )
                            Text(
                                text = "New App Order",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                    },
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(24.dp)
                )

                // Action Buttons - Fixed at bottom outside LazyColumn
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Close Button (leftmost, least prominent)
                    TextButton(
                        onClick = onClose,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Close",
                            color = Color(0xFF6B6B6B),
                            fontSize = 16.sp
                        )
                    }

                    // Reject Button (middle)
                    OutlinedButton(
                        onClick = onReject,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFFD32F2F)
                        ),
                        border = BorderStroke(1.dp, Color(0xFFD32F2F))
                    ) {
                        Text(
                            text = "Reject",
                            fontSize = 16.sp
                        )
                    }

                    // Accept Button (rightmost, most prominent)
                    Button(
                        onClick = onAccept,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            text = "Accept",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}
