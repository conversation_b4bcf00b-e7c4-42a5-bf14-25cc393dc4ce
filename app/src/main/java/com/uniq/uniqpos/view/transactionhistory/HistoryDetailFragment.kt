package com.uniq.uniqpos.view.transactionhistory


import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.RefundEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.FragmentHistoryDetailBinding
import com.uniq.uniqpos.databinding.ListItemOrderHistoryBinding
import com.uniq.uniqpos.databinding.ListItemTaxBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.TaxSales
import com.uniq.uniqpos.model.ViewHeader
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.printer.PrinterManager
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import com.uniq.uniqpos.util.view.DialogAuthorization
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.piutang.PiutangDetailActivity
import com.uniq.uniqpos.view.setting.printer.PrinterDetailActivity
import kotlinx.coroutines.*
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.random.Random

/**
 * A simple [Fragment] subclass.
 *
 */
class HistoryDetailFragment : Fragment() {

    private lateinit var binding: FragmentHistoryDetailBinding
    private val orderList = ArrayList<Order>()
    private val taxList = ArrayList<TaxSales>()
    private var sales: SalesEntity? = null
    private val PRINT_RECEIPT = "receipt"
    private val PRINT_ORDER = "order"
    private val PRINT_ALL = "all"
    private var isTabLayout = false
    private lateinit var employee: Employee
    private lateinit var outlet: Outlet
    private lateinit var viewModel: TransactionHistoryViewModel

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHistoryDetailBinding.inflate(inflater, container, false)
        employee = context?.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java) ?: Employee()
        outlet = context?.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()

        arguments?.apply {
            sales = getParcelable("data") as? SalesEntity
        }

        isTabLayout = resources.getBoolean(R.bool.landscape_only)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        sales?.let { sales ->
            binding.sales = sales
            
            // Set transaction date
            binding.txtTransactionDate.text = sales.timeModified.formatDateTime()
            
            // Set payment method
            val payment = sales.payments?.firstOrNull()
            binding.txtPaymentMethod.text = payment?.method ?: "-"
            binding.txtPaymentBank.text = payment?.bank?.name ?: ""
            binding.txtPaymentBank.visibility = if (payment?.bank != null) View.VISIBLE else View.GONE

            orderList.addAll(sales.orderList ?: ArrayList())

            var subtotal = orderList.sumOf { order ->
                order.subTotal.force(order.isItemVoid) - order.discount.discountNominal.force(order.isItemVoid)
            }
            orderList.forEach { order ->
                subtotal += order.extra.sumOf { extra ->
                    extra.subTotal.force(order.isItemVoid) + extra.discount.discountNominal.force(
                        order.isItemVoid
                    )
                }
            }

            binding.txtSubtotal.text = subtotal.toCurrency()

            taxList.clear()
            taxList.addAll(sales.taxes!!)
            sales.discount?.takeIf { it.discount > 0 }?.let {
                taxList.add(
                    TaxSales(
                        null,
                        "Discount",
                        it.discountNominal,
                        it.discountType,
                        it.discount,
                        Constant.TAX_CATEGORY_DISC
                    )
                )
            }
            sales.discount?.takeIf { it.voucher > 0 }?.let {
                taxList.add(
                    TaxSales(
                        null,
                        "Voucher",
                        it.voucherNominal,
                        it.voucherType,
                        it.voucher,
                        Constant.TAX_CATEGORY_VOUCHER
                    )
                )
            }

            Timber.i("Promotion size : ${sales.promotions?.size}")
            sales.promotions?.forEach { promotion ->
                val promoValue =
                    if (promotion.promoNominal > 0) promotion.promoNominal else promotion.value.safeToInt()
                taxList.add(
                    TaxSales(
                        null,
                        promotion.name,
                        promoValue,
                        "deals",
                        promotion.value.safeToInt(),
                        Constant.TAX_CATEGORY_VOUCHER
                    )
                )
            }
        }

//        val headerInfo = getHeaderInfo()
//        binding.recViewHeader.adapter = object : GlobalAdapter<ListItemHistoryHeaderBinding>(
//            R.layout.list_item_history_header,
//            headerInfo
//        ) {}

        Timber.i(">> Sales chosen : ${Gson().toJson(sales)}")
        binding.recviewHistoryDetail.adapter = object : GlobalAdapter<ListItemOrderHistoryBinding>(
            R.layout.list_item_order_history,
            orderList
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemOrderHistoryBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                
                val order = orderList[position]
                
                // Handle extras
                holder.binding.layoutExtra.removeAllViews()
                order.extra.forEach { extra ->
                    val extraView = layoutInflater.inflate(R.layout.item_extra, holder.binding.layoutExtra, false)
                    extraView.findViewById<TextView>(R.id.txt_extra_name).text = extra.product?.name
                    extraView.findViewById<TextView>(R.id.txt_extra_price).text = extra.subTotal.toCurrency()
                    holder.binding.layoutExtra.addView(extraView)
                }

                // Handle notes
                if (order.note?.isNotBlank() == true || order.info != null) {
                    holder.binding.txtNotes.text = order.note
                    holder.binding.txtNotes.visibility = View.VISIBLE
                } else {
                    holder.binding.txtNotes.visibility = View.GONE
                }

                // Handle promotions
                order.promotion?.let { promo ->
                    val noteText = TextView(context)
                    noteText.setTextColor(Utils.getColor(context, R.color.text_grey_light))
                    noteText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                    noteText.setTypeface(null, Typeface.ITALIC)

                    var promoInfo = promo.name
                    if (promoInfo.isEmpty()) {
                        promoInfo = promo.pomotionTypeFkid.readablePromoType(default = "promotion applied")
                            .lowercase()
                    }
                    if (promo.promotionValue > 0) {
                        promoInfo += " (${(promo.promotionValue * -1).toCurrency()})"
                    }

                    noteText.text = "#$promoInfo"
                    holder.binding.layoutExtra.addView(noteText)
                }
            }
        }

        binding.recViewTax.adapter =
            object : GlobalAdapter<ListItemTaxBinding>(R.layout.list_item_tax, taxList) {}

        binding.btnPrint.setOnClickListener { showPrintOption() }
        binding.btnRefund.setOnClickListener {
            Timber.i("try to refund ${sales?.noNota}")
            val refund = viewModel.salesList.any { it.noNota == sales?.noNota && it.status?.lowercase() == "refund" }
            if (refund) {
                context?.showMessage(getString(R.string.has_been_refunded), "FAILED")
            } else {
                context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                    ?.takeIf { it.openShiftId > 0 }?.let {
                        requestAuthorization(DialogAuthorization.AuthType.REFUND)
                    } ?: run {
                    context?.showMessage(getString(R.string.no_active_shift_msg), "NO ACTIVE SHIFT")
                }
            }
        }

        binding.txtShowPaymentHistory.setOnClickListener {
            viewModel.getPiutangBySaleId(sales?.noNota ?: "")
        }
    }

    private fun getHeaderInfo(): ArrayList<ViewHeader> {
        val payments = arrayListOf<String>()
        val salesPayment =
            sales?.payments?.map { if (it.method == "CARD") "${it.method} (${it.bank?.name})" else it.method }
        sales?.payments?.forEach { payment ->
            if (payment.method == "CARD") payments.add("${payment.method} (${payment.bank?.name})")
            else payments.add(payment.method)
        }
        val result = arrayListOf(
            ViewHeader(getString(R.string.invoice_no), sales?.displayNota.safe()),
            ViewHeader(getString(R.string.payment_method), salesPayment?.joinToString(", ").safe()),
            ViewHeader(getString(R.string.customer), sales?.customer.safe())
        )
        sales?.salesTag?.let { salesTag ->
            result.add(ViewHeader("Order Type", salesTag.name))
        }
        sales?.table?.takeIf { it.isNotBlank() }?.let { table ->
            result.add(ViewHeader(getString(R.string.table), table))
        }
        sales?.customersQty?.takeIf { it > 1 }?.let { pax ->
            result.add(ViewHeader("Pax", pax.toString()))
        }
        return result
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        if (savedInstanceState == null) {
            viewModel = when (activity) {
                // TODO: 12/21/20 somehow getTransactionHistory... return null, this causes force-close
                is MainActivity -> (activity as MainActivity).getTransactionHistoryFragment()!!.viewModel
                else -> (activity as HistoryDetailActivity).viewModel
            }

            initView()
            initViewFeature()
            observeData()
        }
    }

    private fun initView() {
        val isRefund =
            sales?.status?.lowercase() == "refund" || viewModel.salesList.any { it.noNota == sales?.noNota && it.status?.lowercase() == "refund" }
        binding.txtShowPaymentHistory.setVisible(!isRefund && sales?.payments?.any { it.method.lowercase() == "piutang" }
            .safe() && ((isTabLayout && activity is MainActivity) || !isTabLayout))

        // Set refund processor if applicable
        if (sales?.status?.equals("Refund", true).safe()) {
            viewModel.getEmployeeById(sales?.salesRefund?.employeeId.safe())?.let { employee ->
                binding.txtRefundProcessor.text = "Processed by: ${employee.name}"
            }
        }

        updateUiBtnRefund(isRefund)
    }

    private fun updateUiBtnRefund(isRefunded: Boolean) {
        if (isRefunded) {
            binding.btnRefund.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.red_text_disable
                )
            )
            binding.btnRefund.setBackgroundColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.red_background_disable
                )
            )
        }
    }

    private fun initViewFeature() {
        context?.outletFeature()?.let { feature ->
            binding.btnRefund.visibility = (feature.refund && viewModel.enableRefund).visibility()
        }
    }

    private fun observeData() {
        viewModel.piutangNavigation.observe(viewLifecycleOwner) {
            it.getContentIfNotHandled()?.let { piutangEntity ->
                val bundle = Bundle()
                bundle.putParcelable("data", piutangEntity)
                bundle.putParcelable("sales", sales)
                val intent = Intent(context, PiutangDetailActivity::class.java)
                intent.putExtra("data", bundle)
                startActivity(intent)
            }
        }
    }

    private fun requestAuthorization(
        authType: DialogAuthorization.AuthType,
        isAutoCheck: Boolean = true
    ) {
        Timber.d(">>> request authorization for $authType")
        context?.apply {
            DialogAuthorization(this)
                .setAuthType(authType)
                .setAutoCheck(isAutoCheck)
                .setOnAuthorizedListener { employee ->
                    when (authType) {
                        DialogAuthorization.AuthType.REFUND -> refund(employee)
                        DialogAuthorization.AuthType.REPRINT_RECEIPT -> printTransaction(
                            sales!!,
                            PRINT_RECEIPT
                        )
                        DialogAuthorization.AuthType.REPRINT_ORDER -> printTransaction(
                            sales!!,
                            PRINT_ORDER
                        )
                        else -> Timber.i("undefined")
                    }
                }
                .show()
        } ?: run {
            Timber.e("context is null")
        }
    }

    private fun showPrintOption() {
        if (sales == null) {
            context?.showMessage("Please choose transaction first!")
            return
        }

        val options = arrayOf(getString(R.string.cust_receipt), getString(R.string.captain_order))
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.choose_print_fmt))
            .setItems(options) { _, position ->
                if (position == 0) {
                    requestAuthorization(DialogAuthorization.AuthType.REPRINT_RECEIPT)
                } else {
                    requestAuthorization(DialogAuthorization.AuthType.REPRINT_ORDER)
                }
            }
            .show()
    }

    private fun printTransaction(sales: SalesEntity, printType: String, isRefund: Boolean = false) {
        Timber.i("Reprint Nota. Type $printType | NoNota : ${sales.noNota}")
        showDialog(true)
        val employeeSession: Employee =
            context?.employeeList()?.firstOrNull { it.employeeId == sales.employeeID }?.simplify()
                ?: employee

        lifecycleScope.launch(Dispatchers.Main) {
            val printers =
                withContext(Dispatchers.IO) { viewModel.getPrinterSetting() }
            if (printers.isEmpty()) {
                context?.toast(
                    getString(R.string.have_no_printer),
                    level = Level.WARNING
                )
                showDialog(false)
                return@launch
            }

            val printDataList = ArrayList<PendingPrintEntity>()
            if (printType == PRINT_RECEIPT || printType == PRINT_ALL) {
                val saleStatus =
                    if (viewModel.isSalesRefund(sales.noNota)) "Refund" else sales.status
                val printerReceipts = printers.filter { it.settingPrintreceipt == "1" }
                if (printerReceipts.isEmpty()) {
                    context?.toast(
                        getString(R.string.no_printer_for_receipt),
                        level = Level.WARNING
                    )

                    if (printers.size == 1) {
                        context?.showMessage(
                            "${getString(R.string.no_printer_for_receipt)}\n\n${getString(R.string.update_printer_question)}",
                            "INFO",
                            { _, _ ->
                                openPrinterSetting(printers.first())
                            },
                        )
                    }
                }
                printerReceipts.forEach {
                    val notaFormat = PrintNotaUtil.getPrintNotaFormat(
                        sales.copy(status = saleStatus),
                        outlet,
                        employeeSession,
                        false,
                        it.settingPrintpapersize,
                        !isRefund
                    )
                    printDataList.add(
                        PendingPrintEntity(
                            it.address,
                            it.type,
                            notaFormat.first + Constant.PRINTER_CODE_CUT,
                            it.name,
                            System.currentTimeMillis() + Random.nextInt()
                        )
                    )
                    delay(50)
                }
            }

            if (printType == PRINT_ORDER || printType == PRINT_ALL) {
                val printerOrder = printers.filter { it.settingPrintorder == "1" }
                if (printerOrder.isEmpty()) {
                    context?.toast(
                        getString(R.string.no_printer_for_captain_order),
                        level = Level.WARNING
                    )
                    if (printers.size == 1) {
                        context?.showMessage(
                            "${getString(R.string.no_printer_for_captain_order)}\n\n${getString(R.string.update_printer_question)}",
                            "INFO",
                            { _, _ ->
                                openPrinterSetting(printers.first())
                            },
                        )
                    }
                }
                printerOrder.forEach {
                    val notaFormat = PrintNotaUtil.getPrintKitchenFormat(
                        sales,
                        paperSize = it.settingPrintpapersize,
                        isReprint = true
                    )
                    printDataList.add(
                        PendingPrintEntity(
                            it.address,
                            it.type,
                            notaFormat.first + "${Constant.PRINTER_CODE_CUT}",
                            it.name,
                            System.currentTimeMillis() + Random.nextInt()
                        )
                    )
                    delay(50)
                }
            }

            Timber.d("total print data list: ")
            managePrintWifi(printDataList)

            //this cause force close in some device
            val ids = printDataList.map { it.id }
            val printerManager = getPrinterManager()
            val result = withTimeoutOrNull(3500) {
                do {
                    delay(500)
                } while (!printerManager?.isPrinted(ids).safe())
                true
            }

            if (result == null) {
                context?.toast("timeout", level = Level.ERROR)
            }
            showDialog(false)
        }
    }

    private fun openPrinterSetting(printer: PrinterEntity) {
        val intent = Intent(context, PrinterDetailActivity::class.java)
        intent.putExtra("data", printer)
        startActivity(intent)
    }

//    private fun isAllPrinted(ids: List<Long>): Boolean {
//        return if (isTabLayout) {
//            (activity as MainActivity).getTransactionHistoryFragment()?.printerManager?.isPrinted(
//                ids
//            ).safe()
//        } else {
//            (activity as HistoryDetailActivity).printerManager.isPrinted(ids)
//        }
//    }

//    private fun isPrintingInProcess(): Boolean {
//        return if (isTabLayout) {
//            (activity as MainActivity).getTransactionHistoryFragment()?.isPrintingInProcess().safe()
//        } else {
//            (activity as HistoryDetailActivity).printerManager.isPrintingInProcess()
//        }
//    }

    private fun managePrintWifi(printDataList: ArrayList<PendingPrintEntity>) {
        if (isTabLayout && activity is MainActivity) {
            if(activity is MainActivity)
            (activity as MainActivity).getTransactionHistoryFragment()?.beginPrint(printDataList)
        } else if (activity is HistoryDetailActivity) {
            (activity as HistoryDetailActivity).beginPrint(printDataList)
        }else{
            Timber.e("activity is null")
            context?.toast("activity is null", level = Level.ERROR)
        }
    }

    private fun showDialog(isShow: Boolean) {
        if (isTabLayout) {
            (activity as? MainActivity)?.getTransactionHistoryFragment()?.showDialog(isShow)
        } else {
            (activity as? HistoryDetailActivity)?.showDialog(isShow)
        }
    }

    private fun getPrinterManager(): PrinterManager? {
        return if (isTabLayout) {
            (activity as MainActivity?)?.getTransactionHistoryFragment()?.printerManager
        } else {
            (activity as HistoryDetailActivity?)?.printerManager
        }
    }

    private fun refund(employee: Employee) {
        if (sales == null) return
        if (sales?.status?.lowercase() == "refund") {
            context?.showMessage("Item ini sudah di Refund!")
            updateUiBtnRefund(true)
            return
        }

        val refund =
            viewModel.salesList.firstOrNull { it.noNota == sales?.noNota && it.status?.lowercase() == "refund" }
        if (refund != null) {
            context?.showMessage(getString(R.string.transaction_already_refund))
            updateUiBtnRefund(true)
            return
        }

        Timber.i("try to refund ${sales?.noNota} by ${employee.name} (${employee.employeeId})")

        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result.length < 70) {
                view?.hideKeyboard(context)
                viewModel.saveRefund(sales!!, result, employee.employeeId)
                val tmpSales = sales!!.copy(status = "Refund", refundReason = result,
                    salesRefund = RefundEntity(employeeId = employee.employeeId, grandTotal = sales!!.grandTotal))
                printTransaction(tmpSales, PRINT_RECEIPT, true)
                bottomDialog.dismiss()
                binding.sales = tmpSales
//                initView()
                updateUiBtnRefund(true)
//                if (!isTabLayout) {
////                    activity?.finish()
//                }
            } else {
                bottomDialog.setError("your reason is too long!")
            }
        }
        bottomDialog.setModel(
            BottomDialogModel(
                getString(R.string.refund_sales),
                "REFUND",
                sales?.displayNota,
                getString(R.string.give_reason_why_refund),
                getString(R.string.reason)
            )
        )
        bottomDialog.show(childFragmentManager, "refund_reason")
    }

    private fun Long.formatDateTime(): String {
        val date = Date(this)
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        return format.format(date)
    }
}
